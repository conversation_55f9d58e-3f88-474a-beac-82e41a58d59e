# NoDepeg Policy Bypass Vulnerability - Proof of Concept

## Overview

This PoC demonstrates a critical vulnerability in the Enzyme Protocol where the `redeemSharesInKind` function bypasses NoDepeg policy protections that are enforced on `redeemSharesForSpecificAssets`. This creates systematic MEV opportunities during oracle lag periods.

## Vulnerability Summary

**Root Cause**: Architectural inconsistency in policy enforcement between two redemption functions:
- `redeemSharesForSpecificAssets` → Protected by NoDepeg policy validation
- `redeemSharesInKind` → No policy validation, bypasses all protections

**Impact**: Critical MEV - Systematic value extraction from fund participants during stablecoin depeg recovery periods.

## Test File Location

```
tests/tests/policies/NoDepegPolicyBypassPoC.t.sol
```

## Running the PoC Tests

### Prerequisites
- Foundry installed
- Enzyme Protocol test environment set up

### Execute Tests

```bash
# Run the main vulnerability demonstration
forge test --match-test test_noDepegPolicyBypassVulnerability -vvv

# Run selective redemption demonstration  
forge test --match-test test_selectiveRedemptionWithAssetsToSkip -vvv

# Run policy comparison test
forge test --match-test test_policyCorrectlyBlocksSpecificAssetRedemption -vvv

# Run financial impact calculation
forge test --match-test test_financialImpactCalculation -vvv

# Run all PoC tests
forge test --match-contract NoDepegPolicyBypassPoC -vvv
```

## Test Scenarios

### 1. Main Vulnerability Test (`test_noDepegPolicyBypassVulnerability`)

**Timeline Demonstration (T=0 to T=6):**
- T=0: USDC depegs to $0.95 (both market and fund oracle affected)
- T=1: Market recovers to $1.00 (external exchanges restore peg)  
- T=2: Fund oracle LAGS behind, still shows $0.95
- T=3: `redeemSharesForSpecificAssets` is BLOCKED by NoDepeg policy
- T=4: `redeemSharesInKind` BYPASSES policy and allows redemption at stale price
- T=5: Demonstrates MEV extraction opportunity
- T=6: Oracle updates but fund GAV permanently damaged

**Key Assertions:**
- Attacker successfully extracts USDC despite depeg protection
- Fund GAV is permanently reduced
- Demonstrates quantifiable MEV opportunity
- Shows value transfer from fund participants to attacker

### 2. Selective Redemption Test (`test_selectiveRedemptionWithAssetsToSkip`)

Demonstrates how `_assetsToSkip` parameter enables selective asset redemption:
- Fund contains both USDC and DAI
- Attacker uses `_assetsToSkip` to receive only USDC (skips DAI)
- Shows functional equivalence to `redeemSharesForSpecificAssets` but without policy protection

### 3. Policy Comparison Test (`test_policyCorrectlyBlocksSpecificAssetRedemption`)

Direct comparison showing the policy bypass:
- `redeemSharesForSpecificAssets` correctly blocked by NoDepeg policy
- `redeemSharesInKind` bypasses the same policy with identical economic effect
- Demonstrates architectural inconsistency

### 4. Financial Impact Test (`test_financialImpactCalculation`)

Quantifies the financial impact with realistic parameters:
- $1M fund with 10% USDC allocation
- 10% attacker ownership
- 5% price discrepancy during oracle lag
- Calculates extractable value and potential MEV profit

## Expected Test Output

The tests will log detailed financial metrics:

```
Fund AUM: 1000000000000 (1M USDC)
Attacker Ownership %: 10
Price Discrepancy %: 5  
Extracted Amount: 100000000000 (100k USDC)
GAV Reduction: 5000000000 (5k USDC)
Potential MEV Profit: 5000000000 (5k USDC)
Profit as % of Investment: 5
```

## Technical Details

### Setup Components
- **NoDepeg Policy**: Deployed with 5% deviation tolerance for USDC
- **Test Aggregator**: Simulates Chainlink price feed for USDC
- **Fund Configuration**: Created with NoDepeg policy enabled
- **Attack Scenario**: 10% fund ownership, 5% price discrepancy

### Key Code References
- `redeemSharesInKind`: ComptrollerLib.sol:698-746
- `redeemSharesForSpecificAssets`: ComptrollerLib.sol:649-677
- Policy validation bypass: ComptrollerLib.sol:850-854
- NoDepeg policy implementation: NoDepegOnRedeemSharesForSpecificAssetsPolicy.sol

## Vulnerability Validation

The PoC proves:

1. **Policy Bypass**: `redeemSharesInKind` bypasses NoDepeg protections
2. **Selective Redemption**: `_assetsToSkip` enables targeted asset extraction
3. **MEV Opportunity**: Creates systematic arbitrage during oracle lag
4. **Financial Impact**: Quantifiable value transfer from fund to attacker
5. **Architectural Flaw**: Inconsistent security control application

## Remediation

**Recommended Fix**: Extend NoDepeg policy validation to `redeemSharesInKind` when `_assetsToSkip` parameter enables selective asset redemption, ensuring consistent protection across all redemption pathways.

## Bug Bounty Classification

**Impact**: Critical - MEV (Maximal-Extractable Value)
**Rationale**: Creates systematic arbitrage opportunities that extract value from fund participants through architectural security bypass.

---

*This PoC serves as concrete proof for the bug bounty submission demonstrating the NoDepeg policy bypass vulnerability in Enzyme Protocol.*
