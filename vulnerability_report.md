

# Policy bypass in redeemSharesInKind enables Critical MEV extraction during oracle lag periods

## Brief/Intro

The `redeemSharesInKind` function in Enzyme Protocol bypasses depeg protection policies that are enforced in `redeemSharesForSpecificAssets`, creating a systematic MEV opportunity where users can extract value during oracle lag periods by selectively redeeming only depegged assets using the `_assetsToSkip` parameter. This architectural inconsistency allows repeated exploitation that scales with fund size and oracle lag duration, enabling zero-sum value extraction from fund participants.

## Vulnerability Details

### Root Cause: Architectural Inconsistency
The vulnerability stems from inconsistent policy enforcement between two redemption functions:

1. **`redeemSharesForSpecificAssets`** - Enforces policy validation via `validatePolicies` call in ComptrollerLib.sol:850-854
2. **`redeemSharesInKind`** - No policy validation, only proportional asset distribution via ComptrollerLib.sol:698-746

This creates a systematic bypass where the `NoDepegOnRedeemSharesForSpecificAssetsPolicy` protection mechanism can be completely circumvented through an alternative execution pathway with identical economic effects.

### Attack Vector Mechanism
The attack exploits the `_assetsToSkip` parameter during oracle lag periods following depeg recovery:

**Enhanced Timeline Scenario:**
- **T=0**: USDC depegs to $0.95 (both market and fund oracle affected)
- **T=1**: Market recovers to $1.00 (external exchanges restore peg)
- **T=2**: Fund oracle LAGS behind, still shows $0.95
- **T=2.5**: Fund GAV calculation uses stale USDC price via ComptrollerLib.sol:401-427
- **T=3**: Attacker calls `redeemSharesInKind` with all assets except USDC in `_assetsToSkip`
- **T=3.5**: Policy bypass occurs - no validation via ComptrollerLib.sol:850-854 unlike `redeemSharesForSpecificAssets`
- **T=4**: Receives only USDC calculated at stale oracle price ($0.95) using proportional formula from ComptrollerLib.sol:735:
   ```solidity
   payoutAmounts_[i] = IERC20(payoutAssets_[i]).balanceOf(vaultProxy) * sharesToRedeem / sharesSupply
   ```
- **T=5**: Immediately sells USDC on external market at recovered price ($1.00)
- **T=6**: Oracle updates to $1.00, but fund GAV permanently damaged - USDC already extracted at discount
- **Result**: Attacker profits $0.05 per dollar while remaining fund participants bear the loss

**Technical Analysis:**
- User receives exactly their proportional share (no direct theft)
- Vulnerability lies in selective redemption during oracle lag periods
- `NoDepegOnRedeemSharesForSpecificAssetsPolicy` was designed to prevent exactly this scenario
- Policy protection is completely bypassed through alternative function pathway


## Impact Details

### Classification: CRITICAL - MEV (Maximal-Extractable Value)

This creates a **Critical MEV opportunity** under Enzyme's classification system, specifically **"Unintended consequences from Maximal-extractable value (MEV) exploration"**.

### Impact Characteristics
- **Systematic exploitation**: Can be repeated during depeg events
- **Architectural bypass**: Circumvents intended depeg protection policies
- **Zero-sum extraction**: Value extracted from fund participants through price arbitrage
- **No technical barriers**: Any fund participant can exploit using standard function parameters

### Financial Impact Example
- Fund AUM: $10M with 10% USDC allocation ($1M USDC)
- Price difference: 5% ($1.00 market vs $0.95 oracle)
- Attacker ownership: 10% fund shares
- **Extractable value**: $5,000 per attack
- **Fund loss**: $5,000 permanent GAV reduction

## Critical Classification Justification

This vulnerability meets **Critical MEV** classification because:

1. **Architectural Security Bypass**: Exploits inconsistency between functionally equivalent redemption pathways
2. **Systematic Value Extraction**: Creates repeatable MEV opportunities during depeg events
3. **Protocol Security Violation**: Undermines intended NoDepeg policy protection
4. **Quantifiable Financial Impact**: Causes permanent fund GAV reduction with value transfer to attackers

### Why MEV, not Direct Theft
- Attacker receives exactly their proportional share (no excess funds taken)
- Issue is asset valuation timing, not unauthorized fund access
- Problem is architectural inconsistency, not direct fund manipulation

### Bug Bounty Scope - IN-SCOPE
The vulnerability does **NOT** fall under "external stablecoin depegging" exclusions because:
- **Root Cause**: Architectural inconsistency in policy enforcement
- **Code-Based**: Exploits asymmetric security control application in protocol design
- **Security Control Gap**: The bug is the missing protection mechanism

### Recommended Fix
Extend NoDepeg policy validation to `redeemSharesInKind` when `_assetsToSkip` enables selective redemption.

## References

- `redeemSharesInKind`: contracts/release/core/fund/comptroller/ComptrollerLib.sol:698-746
- `redeemSharesForSpecificAssets`: contracts/release/core/fund/comptroller/ComptrollerLib.sol:649-677
- Policy validation bypass: contracts/release/core/fund/comptroller/ComptrollerLib.sol:850-854
- Depeg policy tests: tests/tests/policies/NoDepegOnRedeemSharesForSpecificAssetsPolicy.t.sol:223-286
- NoDepegOnRedeemSharesForSpecificAssetsPolicy implementation
- Enzyme Finance Bug Bounty Program Severity Classifications



