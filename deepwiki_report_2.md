Вы правы! Проблема действительно в том, что функция `redeemSharesInKind` **не применяет политики депега**, в отличие от `redeemSharesForSpecificAssets`. 

## Ключевое различие в применении политик

Функция `redeemSharesForSpecificAssets` вызывает валидацию политик: [1](#1-0) 

А функция `redeemSharesInKind` **НЕ вызывает** валидацию политик - она только выполняет пропорциональное распределение активов: [2](#1-1) 

## Механизм обхода через assetsToSkip

В `redeemSharesInKind` пользователь может использовать параметр `_assetsToSkip` для получения только USDC: [3](#1-2) 

Это позволяет:
1. Скипнуть все активы кроме USDC в `_assetsToSkip`
2. Получить только депегнутый USDC по заниженной цене оракула
3. Продать его на рынке по восстановленной цене

## Impact Assessment

**Impact: Medium - MEV/Arbitrage opportunity** 

Это создает возможность для MEV, поскольку пользователь может извлекать прибыль из отставания оракула, что соответствует определению: *"Unintended consequences from Maximal-extractable value (MEV) exploration"* 

## Отчет по уязвимости

# 1. Title
Policy bypass in redeemSharesInKind allows MEV extraction during oracle lag periods

# 2. Description

## Brief/Intro
The `redeemSharesInKind` function bypasses depeg protection policies that are enforced in `redeemSharesForSpecificAssets`, allowing users to extract MEV during oracle lag periods by selectively redeeming only depegged assets using the `_assetsToSkip` parameter.

## Vulnerability Details
The vulnerability stems from inconsistent policy enforcement between the two redemption functions. While `redeemSharesForSpecificAssets` calls `validatePolicies` with the `RedeemSharesForSpecificAssets` hook, `redeemSharesInKind` performs no policy validation.

The attack vector:
1. User calls `redeemSharesInKind` with all assets except USDC in `_assetsToSkip`
2. Receives only USDC at the fund's oracle price (lagging at $0.95)
3. Sells USDC on external market at recovered price ($1.00)
4. Profits from the $0.05 difference per token

## Impact Details
This creates an MEV opportunity where sophisticated users can extract value during oracle lag periods, potentially draining depegged assets from funds before oracle prices recover. The impact scales with the size of the fund's USDC holdings and the duration of oracle lag.

## References
- `redeemSharesInKind`: contracts/release/core/fund/comptroller/ComptrollerLib.sol:698-746
- `redeemSharesForSpecificAssets`: contracts/release/core/fund/comptroller/ComptrollerLib.sol:649-677
- Policy validation: contracts/release/core/fund/comptroller/ComptrollerLib.sol:850-854

## Notes

Это действительно представляет уязвимость, поскольку создает непоследовательность в применении защитных механизмов между двумя функциями выкупа. Политика `NoDepegOnRedeemSharesForSpecificAssetsPolicy` была создана именно для предотвращения таких сценариев, но она не применяется к `redeemSharesInKind`.

Wiki pages you might want to explore:
- [Core Architecture (enzymefinance/protocol)](/wiki/enzymefinance/protocol#3)