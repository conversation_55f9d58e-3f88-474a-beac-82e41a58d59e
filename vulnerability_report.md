

# Policy bypass in redeemSharesInKind enables Critical MEV extraction during oracle lag periods

## Brief/Intro

The `redeemSharesInKind` function in Enzyme Protocol bypasses depeg protection policies that are enforced in `redeemSharesForSpecificAssets`, creating a systematic MEV opportunity where users can extract value during oracle lag periods by selectively redeeming only depegged assets using the `_assetsToSkip` parameter. This architectural inconsistency allows repeated exploitation that scales with fund size and oracle lag duration, enabling zero-sum value extraction from fund participants.

## Vulnerability Details

### Root Cause: Architectural Inconsistency
The vulnerability stems from inconsistent policy enforcement between two redemption functions:

1. **`redeemSharesForSpecificAssets`** - Enforces policy validation via `validatePolicies` call in ComptrollerLib.sol:850-854
2. **`redeemSharesInKind`** - No policy validation, only proportional asset distribution via ComptrollerLib.sol:698-746

This creates a systematic bypass where the `NoDepegOnRedeemSharesForSpecificAssetsPolicy` protection mechanism can be completely circumvented through an alternative execution pathway with identical economic effects.

### Attack Vector Mechanism
The attack exploits the `_assetsToSkip` parameter during oracle lag periods following depeg recovery:

**Enhanced Timeline Scenario:**
- **T=0**: USDC depegs to $0.95 (both market and fund oracle affected)
- **T=1**: Market recovers to $1.00 (external exchanges restore peg)
- **T=2**: Fund oracle LAGS behind, still shows $0.95
- **T=2.5**: Fund GAV calculation uses stale USDC price via ComptrollerLib.sol:401-427
- **T=3**: Attacker calls `redeemSharesInKind` with all assets except USDC in `_assetsToSkip`
- **T=3.5**: Policy bypass occurs - no validation via ComptrollerLib.sol:850-854 unlike `redeemSharesForSpecificAssets`
- **T=4**: Receives only USDC calculated at stale oracle price ($0.95) using proportional formula from ComptrollerLib.sol:735:
   ```solidity
   payoutAmounts_[i] = IERC20(payoutAssets_[i]).balanceOf(vaultProxy) * sharesToRedeem / sharesSupply
   ```
- **T=5**: Immediately sells USDC on external market at recovered price ($1.00)
- **T=6**: Oracle updates to $1.00, but fund GAV permanently damaged - USDC already extracted at discount
- **Result**: Attacker profits $0.05 per dollar while remaining fund participants bear the loss

**Technical Analysis:**
- User receives exactly their proportional share (no direct theft)
- Vulnerability lies in selective redemption during oracle lag periods
- `NoDepegOnRedeemSharesForSpecificAssetsPolicy` was designed to prevent exactly this scenario
- Policy protection is completely bypassed through alternative function pathway

### Exploitation Constraints and Feasibility
- **Share Limitation**: Attacker can only redeem assets proportional to their fund ownership
- **Oracle Dependency**: Requires oracle lag during stablecoin price recovery (common during high volatility)
- **Asset Availability**: Requires sufficient liquidity of target depegged asset in fund portfolio
- **High Probability**: Stablecoin depegs occur regularly (USDC March 2023, DAI November 2022)
- **No Technical Barriers**: Any user can exploit using standard function parameters

## Impact Details

### Classification: CRITICAL - MEV (Maximal-Extractable Value)

This creates a **Critical MEV opportunity** under Enzyme's classification system, specifically **"Unintended consequences from Maximal-extractable value (MEV) exploration"**.

### Impact Characteristics
- **Systematic exploitation**: Can be repeated during every depeg/oracle lag event
- **Architectural bypass**: Violates the intended security model by circumventing depeg policies
- **Scalable damage**: Impact grows proportionally with fund size and asset holdings
- **Zero-sum extraction**: Value extracted from fund participants through price arbitrage
- **Automated potential**: MEV bots can monitor oracle lag and exploit systematically
- **No technical barriers**: Any fund participant can exploit using standard function parameters

### Financial Impact Example
**Realistic Scenario**:
- Fund AUM: $10M with 10% USDC allocation ($1M USDC)
- Oracle lag: 5% price difference ($1.00 market vs $0.95 oracle)
- Attacker ownership: 10% fund shares
- **Extractable value**: $0.05 × $100,000 = $5,000 per attack
- **Fund loss**: $5,000 permanent GAV reduction

### Systemic Risk
The vulnerability enables sophisticated actors to systematically drain depegged assets from funds before oracle prices recover, creating an unfair advantage and undermining the protocol's risk management framework. Repeated exploitation during volatile periods can significantly compound fund value erosion.

## Critical Classification Justification

### Technical Criteria Analysis
This vulnerability meets **Critical MEV** classification based on the following technical factors:

1. **Architectural Security Bypass**: Exploits fundamental inconsistency in security control application between functionally equivalent redemption pathways

2. **Systematic Value Extraction**: Creates repeatable, predictable MEV opportunities during common market conditions (oracle lag periods)

3. **Protocol Security Model Violation**: Undermines the intended protection mechanism (NoDepeg policy) through alternative execution pathway

4. **Quantifiable Financial Impact**: Causes measurable, permanent reduction in fund GAV with value transfer to attackers

### Distinction from Direct Theft
**Why MEV, not Direct Theft:**
- Attacker receives exactly their proportional share allocation (no excess funds taken)
- Vulnerability lies in asset valuation timing, not unauthorized fund access
- Technical execution follows correct proportional distribution formula
- Issue is architectural inconsistency, not direct fund manipulation

### Bug Bounty Scope Analysis
**IN-SCOPE Vulnerability Rationale:**

The vulnerability does **NOT** fall under "external stablecoin depegging" exclusions because:
- **Root Cause**: Architectural inconsistency in policy enforcement, not external depeg events
- **Code-Based Vulnerability**: Exploits asymmetric security control application in protocol design
- **Independent of Depeg Causation**: Attack exploits protocol design flaw regardless of depeg source
- **Security Control Gap**: The bug is the missing protection mechanism, not the external price movement

### Recommended Remediation
Implement consistent policy enforcement across all redemption pathways by extending NoDepeg policy validation to `redeemSharesInKind` when `_assetsToSkip` parameter enables selective asset redemption, ensuring uniform protection against selective redemption arbitrage.

## References

- `redeemSharesInKind`: contracts/release/core/fund/comptroller/ComptrollerLib.sol:698-746
- `redeemSharesForSpecificAssets`: contracts/release/core/fund/comptroller/ComptrollerLib.sol:649-677
- Policy validation bypass: contracts/release/core/fund/comptroller/ComptrollerLib.sol:850-854
- Depeg policy tests: tests/tests/policies/NoDepegOnRedeemSharesForSpecificAssetsPolicy.t.sol:223-286
- NoDepegOnRedeemSharesForSpecificAssetsPolicy implementation
- Enzyme Finance Bug Bounty Program Severity Classifications



