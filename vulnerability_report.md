

# Policy bypass in redeemSharesInKind allows systematic MEV extraction during oracle lag periods

## Brief/Intro

The Enzyme Protocol contains a critical architectural vulnerability where the `redeemSharesInKind` function bypasses NoDepeg policy protections that are enforced on `redeemSharesForSpecificAssets`. This inconsistency in security controls allows systematic MEV extraction during oracle lag periods, where attackers can selectively redeem undervalued assets and immediately arbitrage them on external markets, creating repeatable value extraction at the expense of remaining fund participants.

## Vulnerability Details

### Root Cause: Architectural Inconsistency
The vulnerability stems from asymmetric policy enforcement between two functionally equivalent redemption pathways:

1. **Protected Function**: `redeemSharesForSpecificAssets` enforces policy validation including NoDepeg protections
2. **Unprotected Function**: `redeemSharesInKind` bypasses all policy checks, including NoDepeg protections

This architectural inconsistency creates a systematic bypass of intended security controls, undermining the protocol's protection mechanisms against selective redemption arbitrage.

### Technical Implementation Analysis
The exploit leverages the `_assetsToSkip` parameter in `redeemSharesInKind`:

```solidity
// Proportional calculation - technically correct
payoutAmounts_[i] = IERC20(payoutAssets_[i]).balanceOf(vaultProxy) * sharesToRedeem / sharesSupply
```

**Key Technical Points:**
- Attacker receives exactly their proportional share of assets (no direct theft)
- The vulnerability lies in asset valuation during oracle lag periods
- `_assetsToSkip` enables selective redemption functionally identical to `redeemSharesForSpecificAssets`
- No policy validation occurs, bypassing NoDepeg protections

### Attack Mechanism
1. **Precondition**: Stablecoin (e.g., USDC) experiences depeg event, affecting both market and oracle prices
2. **Oracle Lag Window**: Market recovers to $1.00, but fund's oracle remains at depeg price ($0.95)
3. **Selective Redemption**: Attacker calls `redeemSharesInKind` with all assets except USDC in `_assetsToSkip`
4. **Value Extraction**: Receives proportional USDC allocation valued at stale oracle price ($0.95)
5. **Arbitrage**: Immediately sells USDC on external market at recovered price ($1.00)
6. **MEV Realization**: Profits $0.05 per dollar while fund GAV decreases permanently

### Technical Constraints
- **Proportional Limitation**: Limited to attacker's existing share percentage in the fund
- **Oracle Dependency**: Requires oracle lag during price recovery (common during high volatility)
- **Asset Liquidity**: Requires sufficient target asset holdings in fund portfolio
- **Timing Window**: Must execute during oracle lag period before price updates

## Impact Details

### Classification: CRITICAL - MEV (Maximal-Extractable Value)

This vulnerability qualifies as **Critical** under Enzyme's classification system, specifically as **"Unintended consequences from Maximal-extractable value (MEV) exploration"**.

### MEV Classification Rationale
**Definition Alignment**: The attack creates systematic arbitrage opportunities where "somebody not participating in the protocol is making a profit" through exploitation of oracle lag periods, fitting the precise MEV definition in Enzyme's severity framework.

**Key MEV Characteristics:**
- **Systematic Exploitation**: Creates predictable, repeatable extraction opportunities during every stablecoin depeg recovery
- **Automated Potential**: Can be executed by MEV bots monitoring oracle lag conditions
- **Value Transfer Mechanism**: Extracts value from fund participants through price arbitrage without providing protocol benefit
- **Security Control Bypass**: Exploits architectural inconsistency to circumvent intended protection mechanisms

### Financial Impact Analysis
**Impact Formula**: `(Market_Price - Oracle_Price) × Asset_Amount × Attacker_Share_Percentage`

**Realistic Scenario**:
- Fund AUM: $10M with 10% USDC allocation ($1M USDC)
- Oracle lag: 5% price difference ($1.00 market vs $0.95 oracle)
- Attacker ownership: 10% fund shares
- **Extractable value**: $0.05 × $100,000 = $5,000 per attack
- **Fund loss**: $5,000 permanent GAV reduction

### Impact Mechanics
1. **Permanent GAV Reduction**: When oracle updates, fund cannot recover the extracted value as USDC was redeemed at undervalued price
2. **Wealth Transfer**: Value systematically transfers from remaining fund participants to attacker
3. **Compounding Effect**: Repeated exploitation during volatile periods compounds fund value erosion
4. **Scalable Damage**: Impact scales directly with fund size, asset allocation, and price discrepancies

## Critical Classification Justification

### Technical Criteria Analysis
This vulnerability meets **Critical MEV** classification based on the following technical factors:

1. **Architectural Security Bypass**: Exploits fundamental inconsistency in security control application between functionally equivalent redemption pathways

2. **Systematic Value Extraction**: Creates repeatable, predictable MEV opportunities during common market conditions (oracle lag periods)

3. **Protocol Security Model Violation**: Undermines the intended protection mechanism (NoDepeg policy) through alternative execution pathway

4. **Quantifiable Financial Impact**: Causes measurable, permanent reduction in fund GAV with value transfer to attackers

### Distinction from Direct Theft
**Why MEV, not Direct Theft:**
- Attacker receives exactly their proportional share allocation (no excess funds taken)
- Vulnerability lies in asset valuation timing, not unauthorized fund access
- Technical execution follows correct proportional distribution formula
- Issue is architectural inconsistency, not direct fund manipulation

### Bug Bounty Scope Analysis
**IN-SCOPE Vulnerability Rationale:**

The vulnerability does **NOT** fall under "external stablecoin depegging" exclusions because:
- **Root Cause**: Architectural inconsistency in policy enforcement, not external depeg events
- **Code-Based Vulnerability**: Exploits asymmetric security control application in protocol design
- **Independent of Depeg Causation**: Attack exploits protocol design flaw regardless of depeg source
- **Security Control Gap**: The bug is the missing protection mechanism, not the external price movement

### Recommended Remediation
Implement consistent policy enforcement across all redemption pathways by extending NoDepeg policy validation to `redeemSharesInKind` when `_assetsToSkip` parameter enables selective asset redemption, ensuring uniform protection against selective redemption arbitrage.

## References

- Enzyme Finance NoDepeg Policy Documentation
- `redeemSharesInKind` function implementation
- `redeemSharesForSpecificAssets` function implementation  
- Historical stablecoin depeg events (USDC March 2023, DAI November 2022)
- Enzyme Finance Bug Bounty Program Scope and Impact Classifications



