


# NoDepeg policy bypass in redeemSharesInKind function leads to critical MEV extraction during stablecoin depeg recovery

## Brief/Intro

The Enzyme Protocol contains a critical vulnerability where the `redeemSharesInKind` function bypasses NoDepeg policy protections that are enforced on `redeemSharesForSpecificAssets`. This allows attackers to exploit stablecoin depeg scenarios by selectively redeeming only depegged assets (like USDC) at oracle prices while the market has recovered, creating systematic MEV opportunities that drain value from other fund participants.

## Vulnerability Details

### Root Cause Analysis
The core issue lies in the asymmetric application of the `NoDepegOnRedeemSharesForSpecificAssetsPolicy` between two redemption functions in the Enzyme Protocol:

1. **Protected Function**: `redeemSharesForSpecificAssets` calls policy validation via policy manager
2. **Unprotected Function**: `redeemSharesInKind` intentionally bypasses all policy checks as documented in the codebase

The NoDepeg policy exists specifically to prevent selective redemption arbitrage during stablecoin depeg events, as evidenced by the test suite and policy implementation. However, this protection only applies to one redemption pathway, creating a bypass through `redeemSharesInKind`.

### Technical Implementation Gap
The vulnerability is exploited through the `_assetsToSkip` parameter in `redeemSharesInKind`. The function processes this parameter by removing specified assets from the payout list, effectively allowing selective redemption:

**Protection Asymmetry:**
- `redeemSharesForSpecificAssets` → Protected by NoDepeg policy validation
- `redeemSharesInKind` with selective `_assetsToSkip` → No policy protection, identical economic effect

This creates a fundamental security gap where the intended protection mechanism can be completely circumvented through an alternative function with equivalent selective redemption capabilities.

### Detailed Attack Mechanism
1. **Setup Phase**: A stablecoin (e.g., USDC) experiences depeg to $0.95, affecting both market prices and the fund's oracle
2. **Recovery Phase**: Market prices recover to $1.00, but the fund's oracle lags behind, still showing $0.95
3. **Exploitation**: Attacker calls `redeemSharesInKind` with `_assetsToSkip` containing all assets except the undervalued USDC
4. **Selective Redemption**: Function bypasses policy checks and redeems only USDC at the stale oracle price ($0.95)
5. **Arbitrage Completion**: Attacker immediately sells USDC on the market at current price ($1.00), profiting $0.05 per dollar

### Attack Constraints and Feasibility
- **Share Limitation**: Attacker can only redeem assets proportional to their share ownership in the fund
- **Oracle Dependency**: Requires oracle lag during stablecoin price recovery (technically common during high volatility)
- **Asset Availability**: Requires sufficient liquidity of the target depegged asset in the fund portfolio
- **High Probability**: Stablecoin depegs occur regularly (USDC March 2023, DAI November 2022)

## Impact Details

### Classification: CRITICAL - MEV (Miner-Extractable Value)

This vulnerability qualifies as **Critical** under Enzyme's classification system, specifically as **"Unintended consequences from Maximal-extractable value (MEV)"**.

### MEV Characteristics Analysis
- **Systematic Arbitrage**: Creates repeatable arbitrage opportunities extractable by automated bots during every stablecoin depeg event
- **Value Extraction**: Profits come at direct expense of other fund participants, fitting the MEV definition: "somebody not participating in the protocol is making a profit"
- **Automation Potential**: MEV bots can monitor oracle lag and automatically exploit this vulnerability
- **No External Manipulation**: Attack requires no special privileges or external market manipulation

### Financial Impact Calculation
**Per-attack profit formula**: `(Market_Price - Oracle_Price) × USDC_Amount_Redeemed × Attacker_Share_Percentage`

**Concrete Example**:
- Fund: $10M AUM with 10% USDC allocation ($1,000,000 USDC)
- Depeg scenario: 5% price difference ($1.00 market vs $0.95 oracle)
- Attacker with 10% fund ownership can extract: `$0.05 × $100,000 = $5,000 profit`
- Fund participants collectively lose: $5,000 in asset value

### Damage Assessment
1. **Direct Losses**: Fund sells assets below market value, reducing NAV for remaining investors
2. **Wealth Transfer**: Systematic value transfer from fund participants to attackers
3. **Fund Dilution**: Remaining shareholders bear the cost as fund NAV decreases by the arbitrage amount
4. **Systemic Risk**: Repeated exploitation during volatile periods can significantly drain fund value
5. **Scalability**: Attack damage scales with fund size, depeg magnitude, and attacker's share percentage

## Impact Justification

### Critical Classification Rationale
This vulnerability meets the CRITICAL MEV classification because:

1. **MEV Definition Satisfied**: Creates systematic arbitrage opportunities where "somebody not participating in the protocol is making a profit" through price arbitrage at the expense of fund participants

2. **Security Control Bypass**: Circumvents the intended NoDepeg policy protections through an alternative code pathway with identical economic effects

3. **Direct Financial Harm**: Causes measurable, quantifiable losses to fund participants through systematic value extraction

4. **Code Logic Flaw**: Based on asymmetric policy application rather than external market conditions

### Scope Analysis - IN-SCOPE Vulnerability
This vulnerability does **NOT** fall under the "external stablecoin depegging" exclusion because:

- **Root Cause**: The vulnerability exists in the protocol's design decision to bypass policies for `redeemSharesInKind`
- **Code Bug**: Attack exploits a code bug (asymmetric policy application) rather than relying solely on external depeg events
- **Bug Bounty Exclusion Interpretation**: The exclusion states "attacks involving the depegging of an external stablecoin where the attacker does not directly cause the depegging **due to a bug in code**"
- **Key Distinction**: Here, the depegging is NOT caused by the bug - the bug is the missing protection mechanism that should prevent exploitation during depeg recovery

The attack exploits a fundamental gap in the protocol's security model, making it a valid in-scope vulnerability under the bug bounty program.

### Recommended Fix
Extend NoDepeg policy enforcement to the `redeemSharesInKind` function, specifically when `_assetsToSkip` parameter is used to selectively exclude assets, creating the same economic effect as `redeemSharesForSpecificAssets`. This would ensure consistent protection across all redemption pathways.

## References

- Enzyme Finance NoDepeg Policy Documentation
- `redeemSharesInKind` function implementation
- `redeemSharesForSpecificAssets` function implementation  
- Historical stablecoin depeg events (USDC March 2023, DAI November 2022)
- Enzyme Finance Bug Bounty Program Scope and Impact Classifications



