# Proof of Concept Summary

## Test File
`tests/tests/policies/NoDepegPolicyBypassPoC.t.sol`

## Quick Run Command
```bash
forge test --match-contract NoDepegPolicyBypassPoC -vvv
```

## What the PoC Demonstrates

### 1. **Policy Bypass Vulnerability**
- `redeemSharesForSpecificAssets` is correctly blocked by NoDepeg policy during USDC depeg
- `redeemSharesInKind` bypasses the same policy with identical economic effect
- Demonstrates architectural inconsistency in security control application

### 2. **MEV Extraction Scenario**
- Timeline T=0 to T=6 showing oracle lag exploitation
- Attacker extracts USDC at $0.95 oracle price while market shows $1.00
- Fund GAV permanently reduced, value transferred to attacker

### 3. **Selective Redemption Mechanism**
- `_assetsToSkip` parameter enables targeted asset extraction
- Functionally equivalent to `redeemSharesForSpecificAssets` but without protection
- Demonstrates the bypass mechanism

### 4. **Financial Impact Quantification**
- $1M fund scenario with 10% attacker ownership
- 5% price discrepancy creates $5,000 MEV opportunity
- Shows scalable damage proportional to fund size and price gaps

## Key Test Results

**Expected Output:**
```
✓ test_noDepegPolicyBypassVulnerability() - Main vulnerability demonstration
✓ test_selectiveRedemptionWithAssetsToSkip() - Selective redemption mechanism  
✓ test_policyCorrectlyBlocksSpecificAssetRedemption() - Policy comparison
✓ test_financialImpactCalculation() - Financial impact quantification
```

**Financial Metrics Logged:**
- Fund AUM: 1,000,000 USDC
- Extracted Amount: 100,000 USDC (10% ownership)
- Potential MEV Profit: 5,000 USDC (5% price discrepancy)
- GAV Reduction: 5,000 USDC (permanent fund damage)

## Vulnerability Proof Points

1. **Architectural Bypass**: Two functionally equivalent functions have asymmetric policy protection
2. **Systematic Exploitation**: Can be repeated during every depeg/oracle lag event  
3. **Zero-Sum Transfer**: Value extracted from fund participants through price arbitrage
4. **No Technical Barriers**: Any fund participant can exploit using standard parameters
5. **Quantifiable Impact**: Measurable financial damage to fund and profit to attacker

## Code References Validated

- Policy bypass occurs at ComptrollerLib.sol:850-854 (validation skipped)
- Proportional calculation at ComptrollerLib.sol:735 (technically correct but exploitable)
- NoDepeg policy correctly implemented but not applied to `redeemSharesInKind`

This PoC provides concrete, runnable proof of the Critical MEV vulnerability for bug bounty submission.
